import 'package:flutter_test/flutter_test.dart';
import 'package:dartz/dartz.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/features/master_data/domain/entities/government_sector.dart';
import 'package:mcdc/features/master_data/domain/entities/member_type.dart';
import 'package:mcdc/features/user/domain/entities/organization.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_government_sectors.dart';
import 'package:mcdc/features/master_data/domain/repositories/master_data_repository.dart';

// Simple mock implementation for testing
class MockMasterDataRepository implements MasterDataRepository {
  final List<GovernmentSector> _governmentSectors;

  MockMasterDataRepository(this._governmentSectors);

  @override
  Future<Either<Failure, List<GovernmentSector>>> getGovernmentSectors() async {
    // Simulate API response
    await Future.delayed(const Duration(milliseconds: 100));
    return Right(_governmentSectors);
  }

  @override
  Future<Either<Failure, List<MemberType>>> getMemberTypes() async {
    throw UnimplementedError(); // Not needed for this test
  }

  @override
  Future<Either<Failure, List<Organization>>> getMinistriesByGovernmentSector(
    int governmentSectorId,
  ) async {
    throw UnimplementedError(); // Not needed for this test
  }
}

void main() {
  group('Government Sector Integration Tests', () {
    late GetGovernmentSectors useCase;
    late MockMasterDataRepository mockRepository;

    // Mock government sectors matching the API response structure
    final mockGovernmentSectors = [
      const GovernmentSector(
        id: 1,
        nameTh: 'ราชการส่วนกลาง',
        nameEn: 'ราชการส่วนกลาง',
      ),
      const GovernmentSector(
        id: 2,
        nameTh: 'องค์กรอิสระ',
        nameEn: 'องค์กรอิสระ',
      ),
      const GovernmentSector(
        id: 3,
        nameTh: 'หน่วยงานอิสระของรัฐ',
        nameEn: 'หน่วยงานอิสระของรัฐ',
      ),
      const GovernmentSector(
        id: 4,
        nameTh: 'ราชการส่วนท้องถิ่น',
        nameEn: 'ราชการส่วนท้องถิ่น',
      ),
      const GovernmentSector(
        id: 5,
        nameTh: 'รัฐวิสาหกิจ',
        nameEn: 'รัฐวิสาหกิจ',
      ),
      const GovernmentSector(
        id: 6,
        nameTh: 'องค์การมหาชน',
        nameEn: 'องค์การมหาชน',
      ),
      const GovernmentSector(
        id: 7,
        nameTh: 'หน่วยธุรการของศาล',
        nameEn: 'หน่วยธุรการของศาล',
      ),
      const GovernmentSector(
        id: 8,
        nameTh: 'หน่วยงานสังกัดรัฐสภาหรือในกำกับของรัฐสภา',
        nameEn: 'หน่วยงานสังกัดรัฐสภาหรือในกำกับของรัฐสภา',
      ),
      const GovernmentSector(
        id: 9,
        nameTh: 'องค์กรตามรัฐธรรมนูญ',
        nameEn: 'องค์กรตามรัฐธรรมนูญ',
      ),
    ];

    setUp(() {
      mockRepository = MockMasterDataRepository(mockGovernmentSectors);
      useCase = GetGovernmentSectors(mockRepository);
    });

    test('should return list of government sectors from API', () async {
      // Act
      final result = await useCase(NoParams());

      // Assert
      expect(result.isRight(), isTrue);
      result.fold(
        (failure) =>
            fail('Expected success but got failure: ${failure.message}'),
        (governmentSectors) {
          expect(governmentSectors, isNotEmpty);
          expect(governmentSectors.length, equals(9));

          // Check specific sectors
          expect(governmentSectors.any((sector) => sector.id == 1), isTrue);
          expect(
            governmentSectors.any(
              (sector) => sector.nameTh == 'ราชการส่วนกลาง',
            ),
            isTrue,
          );
          expect(governmentSectors.any((sector) => sector.id == 5), isTrue);
          expect(
            governmentSectors.any((sector) => sector.nameTh == 'รัฐวิสาหกิจ'),
            isTrue,
          );
        },
      );
    });

    test('should have correct display names', () async {
      // Act
      final result = await useCase(NoParams());

      // Assert
      result.fold(
        (failure) =>
            fail('Expected success but got failure: ${failure.message}'),
        (governmentSectors) {
          final centralGov = governmentSectors.firstWhere(
            (sector) => sector.id == 1,
          );
          expect(centralGov.displayName, equals('ราชการส่วนกลาง'));

          final stateEnterprise = governmentSectors.firstWhere(
            (sector) => sector.id == 5,
          );
          expect(stateEnterprise.displayName, equals('รัฐวิสาหกิจ'));
        },
      );
    });

    test('should handle empty response', () async {
      // Arrange
      final emptyRepository = MockMasterDataRepository([]);
      final emptyUseCase = GetGovernmentSectors(emptyRepository);

      // Act
      final result = await emptyUseCase(NoParams());

      // Assert
      expect(result.isRight(), isTrue);
      result.fold(
        (failure) =>
            fail('Expected success but got failure: ${failure.message}'),
        (governmentSectors) {
          expect(governmentSectors, isEmpty);
        },
      );
    });
  });

  group('GovernmentSector Entity Tests', () {
    test('should create government sector with required fields', () {
      const sector = GovernmentSector(
        id: 1,
        nameTh: 'ราชการส่วนกลาง',
        nameEn: 'Central Government',
      );

      expect(sector.id, equals(1));
      expect(sector.nameTh, equals('ราชการส่วนกลาง'));
      expect(sector.nameEn, equals('Central Government'));
      expect(sector.displayName, equals('ราชการส่วนกลาง'));
    });

    test('should support equality comparison', () {
      const sector1 = GovernmentSector(
        id: 1,
        nameTh: 'ราชการส่วนกลาง',
        nameEn: 'Central Government',
      );

      const sector2 = GovernmentSector(
        id: 1,
        nameTh: 'ราชการส่วนกลาง',
        nameEn: 'Central Government',
      );

      const sector3 = GovernmentSector(
        id: 2,
        nameTh: 'องค์กรอิสระ',
        nameEn: 'Independent Organization',
      );

      expect(sector1, equals(sector2));
      expect(sector1, isNot(equals(sector3)));
    });

    test('should support copyWith method', () {
      const originalSector = GovernmentSector(
        id: 1,
        nameTh: 'ราชการส่วนกลาง',
        nameEn: 'Central Government',
      );

      final copiedSector = originalSector.copyWith(
        nameEn: 'Updated Central Government',
      );

      expect(copiedSector.id, equals(originalSector.id));
      expect(copiedSector.nameTh, equals(originalSector.nameTh));
      expect(copiedSector.nameEn, equals('Updated Central Government'));
    });
  });
}
