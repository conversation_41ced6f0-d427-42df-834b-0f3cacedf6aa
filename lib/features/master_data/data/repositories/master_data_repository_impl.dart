import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/api/network_info.dart';
import '../../domain/entities/member_type.dart';
import '../../domain/entities/government_sector.dart';
import '../../../user/domain/entities/organization.dart';
import '../../domain/repositories/master_data_repository.dart';
import '../datasources/master_data_api_data_source.dart';
import '../models/member_type_model.dart';
import '../models/government_sector_model.dart';
import '../models/ministry_model.dart';

class MasterDataRepositoryImpl implements MasterDataRepository {
  final MasterDataApiDataSource apiDataSource;
  final NetworkInfo networkInfo;

  const MasterDataRepositoryImpl({
    required this.apiDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<MemberType>>> getMemberTypes() async {
    if (await networkInfo.isConnected) {
      try {
        final result = await apiDataSource.getMemberTypes();

        if (result.isSuccess) {
          final memberTypes =
              result.data!.map((model) => model.toEntity()).toList();
          return Right(memberTypes);
        } else {
          return Left(
            ServerFailure(
              message: result.errorMessage ?? 'Failed to get member types',
            ),
          );
        }
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<GovernmentSector>>> getGovernmentSectors() async {
    if (await networkInfo.isConnected) {
      try {
        final result = await apiDataSource.getGovernmentSectors();

        if (result.isSuccess) {
          final governmentSectors =
              result.data!.map((model) => model.toEntity()).toList();
          return Right(governmentSectors);
        } else {
          return Left(
            ServerFailure(
              message:
                  result.errorMessage ?? 'Failed to get government sectors',
            ),
          );
        }
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<Organization>>> getMinistriesByGovernmentSector(
    int governmentSectorId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await apiDataSource.getMinistriesByGovernmentSector(
          governmentSectorId,
        );

        if (result.isSuccess) {
          final ministries =
              result.data!.map((model) => model.toEntity()).toList();
          return Right(ministries);
        } else {
          return Left(
            ServerFailure(
              message: result.errorMessage ?? 'Failed to get ministries',
            ),
          );
        }
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }
}
